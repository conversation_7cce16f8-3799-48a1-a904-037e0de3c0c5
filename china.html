<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>china-3D</title>
  <style>
    html body {
      height: 100%;
      width: 100%;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }

    #provinceInfo {
      position: absolute;
      z-index: 2;
      background: white;
      padding: 10px;
      visibility: hidden;
    }
  </style>
  <script src="./js/three.js"></script>
  <script src="./js/OrbitControls.js"></script>
  <script src="./js/d3-array.v1.min.js"></script>
  <script src="./js/d3-geo.v1.min.js"></script>

</head>

<body>
  <div id="provinceInfo"></div>
</body>
<script>
  class lineMap {
    constructor(container) {
      this.container = container ? container : document.body;
    }

    init() {
      this.provinceInfo = document.getElementById('provinceInfo');
      // 渲染器
      this.renderer = new THREE.WebGLRenderer();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
      this.container.appendChild(this.renderer.domElement);

      // CSS2D 渲染器用于显示文字标签
      this.labelRenderer = new THREE.CSS2DRenderer();
      this.labelRenderer.setSize(window.innerWidth, window.innerHeight);
      this.labelRenderer.domElement.style.position = 'absolute';
      this.labelRenderer.domElement.style.top = '0px';
      this.labelRenderer.domElement.style.pointerEvents = 'none';
      this.container.appendChild(this.labelRenderer.domElement);

      // 场景
      this.scene = new THREE.Scene();

      // 相机 透视相机
      this.camera = new THREE.PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
      this.camera.position.set(0, -70, 50);
      this.camera.lookAt(0, 0, 0);

      this.setController(); // 设置控制

      this.setLight(); // 设置灯光

      this.setRaycaster();

      this.animate();

      this.loadMapData(); // 直接加载地图数据

      this.setResize(); // 绑定浏览器缩放事件
    }

    setResize() {
      let _this = this;
      window.addEventListener('resize', function () {
        _this.renderer.setSize(window.innerWidth, window.innerHeight);
        _this.labelRenderer.setSize(window.innerWidth, window.innerHeight);
        _this.camera.aspect = window.innerWidth / window.innerHeight;
        _this.camera.updateProjectionMatrix();
      })
    }

    loadMapData() {
      let _this = this;

      // 加载json文件
      let loader = new THREE.FileLoader();
      loader.load('./json/qingyuan.json', function (data) {
        let jsonData = JSON.parse(data);
        _this.initMap(jsonData);
      });
    }

    loadFont() { //加载中文字体
      var loader = new THREE.FontLoader();
      var _this = this;
      loader.load('fonts/chinese.json', function (response) {
        console.log('字体加载成功');
        _this.font = response;
        _this.loadMapData();
      }, undefined, function(error) {
        console.error('字体加载失败:', error);
        // 如果字体加载失败，仍然加载地图数据
        _this.loadMapData();
      });

    }

    createText(text, position, scale = 1) {
      try {
        // 创建 HTML 元素
        var textDiv = document.createElement('div');
        textDiv.className = 'label';
        textDiv.textContent = text;
        textDiv.style.color = '#000';
        textDiv.style.fontSize = '14px';
        textDiv.style.fontFamily = 'Arial, sans-serif';
        textDiv.style.fontWeight = 'bold';
        textDiv.style.textShadow = '1px 1px 2px rgba(255,255,255,0.8)';
        textDiv.style.pointerEvents = 'none';
        textDiv.style.textAlign = 'center';

        // 创建 CSS2D 对象
        var textLabel = new THREE.CSS2DObject(textDiv);
        textLabel.position.set(position.x, position.y, position.z);

        this.map.add(textLabel); // 添加到地图对象
        console.log('创建文字标签:', text);
      } catch (error) {
        console.error('创建文字标签失败:', text, error);
      }
    }

    initMap(chinaJson) {
      // 建一个空对象存放对象
      this.map = new THREE.Object3D();

      let _this = this;

      // 墨卡托投影转换
      let projection = d3.geoMercator().center([104.0, 37.5]).scale(80).translate([0, 0]);

      // 自动根据加载的 geojson 调整投影的 scale/translate 到合适大小
      // 并在后续构建几何时把投影结果居中到场景原点，避免小范围数据（例如清远）显示过小或偏移
      try {
        // 使用 fitSize 让投影先适配画布（d3-geo 提供）
        projection.fitSize([window.innerWidth * 0.8, window.innerHeight * 0.8], chinaJson);
      } catch (e) {
        // 如果环境没有 fitSize，也可以继续使用默认 projection
        console.warn('projection.fitSize failed, using default scale', e);
      }

      // 记录投影后的中心点，后面在创建几何时把所有点平移到以该点为中心的坐标系
      const geoCentroid = d3.geoCentroid(chinaJson);
      const projCenter = projection(geoCentroid);

      // 用于计算几何包围盒，以便后面调整相机距离
      let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

      chinaJson.features.forEach(elem => {
        // 定一个省份3D对象
        const province = new THREE.Object3D();
        // 每个的 坐标 数组
        const coordinates = elem.geometry.coordinates;
        // 循环坐标数组
        coordinates.forEach(multiPolygon => {

          multiPolygon.forEach(polygon => {
            const shape = new THREE.Shape();
            const lineMaterial = new THREE.LineBasicMaterial({
              color: 'white'
            });
            const lineGeometry = new THREE.Geometry();

            for (let i = 0; i < polygon.length; i++) {
              const [x, y] = projection(polygon[i]);
              // 将投影后的点平移，使 projCenter 位于场景原点
              const px = x - projCenter[0];
              const py = -y - projCenter[1];

              if (i === 0) {
                shape.moveTo(px, py);
              }
              shape.lineTo(px, py);
              // 减小线的 z 值，避免顶部过高
              lineGeometry.vertices.push(new THREE.Vector3(px, py, 0.22));

              // 更新包围盒
              if (px < minX) minX = px;
              if (px > maxX) maxX = px;
              if (py < minY) minY = py;
              if (py > maxY) maxY = py;
            }

            const extrudeSettings = {
              depth: 0.2, // 缩短高度，按需调整
              bevelEnabled: false
            };

            const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
            const material = new THREE.MeshBasicMaterial({
              color: '#02A1E2',
              transparent: true,
              opacity: 0.6
            });
            const material1 = new THREE.MeshBasicMaterial({
              color: '#3480C4',
              transparent: true,
              opacity: 0.5
            });
            /* const material = new THREE.MeshBasicMaterial({ color: '#dedede', transparent: false, opacity: 0.6 });
            const material1 = new THREE.MeshBasicMaterial({ color: '#dedede', transparent: false, opacity: 0.5 }); */
            const mesh = new THREE.Mesh(geometry, [material, material1]);
            const line = new THREE.Line(lineGeometry, lineMaterial);
            province.add(mesh);
            province.add(line)

          })

        })

        // 将geo的属性放到省份模型中
        province.properties = elem.properties;
        if (elem.properties.contorid) {
          const [x, y] = projection(elem.properties.contorid);
          province.properties._centroid = [x, y];
        }

        // 保存区域中心点信息，稍后创建文字
        if (elem.properties.center) {
          const [cx, cy] = projection(elem.properties.center);
          province.properties._textCenter = [cx - projCenter[0], -cy - projCenter[1]];
        } else if (elem.properties.centroid) {
          const [cx, cy] = projection(elem.properties.centroid);
          province.properties._textCenter = [cx - projCenter[0], -cy - projCenter[1]];
        }

        _this.map.add(province);

      })

      this.scene.add(this.map);

      // 计算包围盒并调整相机，使地图充满视野
      let mapScale = 1; // 默认缩放比例
      try {
        if (isFinite(minX) && isFinite(minY) && isFinite(maxX) && isFinite(maxY)) {
          const width = maxX - minX;
          const height = maxY - minY;
          const maxDim = Math.max(width, height);

          // 计算中心（投影坐标系）
          const centerX = (minX + maxX) / 2;
          const centerY = (minY + maxY) / 2;

          // 目标世界尺寸（以像素为参考），用来决定缩放比例
          const targetSize = Math.min(window.innerWidth, window.innerHeight) * 0.25;
          mapScale = targetSize / (maxDim || 1);

          // 对整个 map 做统一缩放（包含高度），并居中
          this.map.scale.set(mapScale, mapScale, mapScale);
          this.map.position.set(-centerX * mapScale, -centerY * mapScale, 0);

          // 根据 targetSize 计算合适相机距离
          const fov = this.camera.fov * (Math.PI / 180);
          const visibleHeight = targetSize / 0.7;
          const distance = visibleHeight / (2 * Math.tan(fov / 2));

          this.camera.position.set(0, -distance * 0.3, distance);
          this.camera.lookAt(new THREE.Vector3(0, 0, 0));

          if (this.controller) {
            this.controller.target.set(0, 0, 0);
            this.controller.update();
          }
        }
      } catch (e) {
        console.warn('adjust camera failed', e);
      }

      // 在地图缩放完成后添加文字标签
      this.map.children.forEach(province => {
        if (province.properties && province.properties.name && province.properties._textCenter) {
          const [centerX, centerY] = province.properties._textCenter;
          this.createText(province.properties.name, {
            x: centerX,
            y: centerY,
            z: 0.25
          }, mapScale);
        }
      });
    }

    setRaycaster() {
      this.raycaster = new THREE.Raycaster();
      this.mouse = new THREE.Vector2();
      this.eventOffset = {};
      var _this = this;

      function onMouseMove(event) {

        // calculate mouse position in normalized device coordinates
        // (-1 to +1) for both components

        _this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
        _this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
        _this.eventOffset.x = event.clientX;
        _this.eventOffset.y = event.clientY;
        this.provinceInfo.style.left = _this.eventOffset.x + 2 + 'px';
        this.provinceInfo.style.top = _this.eventOffset.y + 2 + 'px';
      }

      window.addEventListener('mousemove', onMouseMove, false);

    }

    setLight() {
      let ambientLight = new THREE.AmbientLight(0xffffff); // 环境光
      this.scene.add(ambientLight);
    }

    setController() {
      this.controller = new THREE.OrbitControls(this.camera, this.renderer.domElement);
      /* this.controller.enablePan = false; // 禁止右键拖拽

      this.controller.enableZoom = true; // false-禁止右键缩放
      
      this.controller.maxDistance = 200; // 最大缩放 适用于 PerspectiveCamera
      this.controller.minDistance = 50; // 最大缩放

      this.controller.enableRotate = true; // false-禁止旋转 */

      /* this.controller.minZoom = 0.5; // 最小缩放 适用于OrthographicCamera
      this.controller.maxZoom = 2; // 最大缩放 */

    }

    animate() {
      requestAnimationFrame(this.animate.bind(this));
      // this.cube.rotation.x += 0.05;
      // this.cube.rotation.y += 0.05;
      this.raycaster.setFromCamera(this.mouse, this.camera);

      // calculate objects intersecting the picking ray
      var intersects = this.raycaster.intersectObjects(this.scene.children, true);
      if (this.activeInstersect && this.activeInstersect.length > 0) { // 将上一次选中的恢复颜色
        this.activeInstersect.forEach(element => {
          element.object.material[0].color.set('#02A1E2');
          element.object.material[1].color.set('#3480C4');
        });
      }

      this.activeInstersect = []; // 设置为空

      for (var i = 0; i < intersects.length; i++) {
        if (intersects[i].object.material && intersects[i].object.material.length === 2) {
          this.activeInstersect.push(intersects[i]);
          intersects[i].object.material[0].color.set(0xff0000);
          intersects[i].object.material[1].color.set(0xff0000);
          break; // 只取第一个
        }
      }
      this.createProvinceInfo();

      this.renderer.render(this.scene, this.camera);
      this.labelRenderer.render(this.scene, this.camera);
    }

    createProvinceInfo() { // 显示省份的信息
      if (this.activeInstersect.length !== 0 && this.activeInstersect[0].object.parent.properties.name) {
        var properties = this.activeInstersect[0].object.parent.properties;

        // 可以显示更详细的信息，比如行政代码等
        this.provinceInfo.textContent = properties.name + ' (代码: ' + properties.adcode + ')';

        this.provinceInfo.style.visibility = 'visible';
      } else {
        this.provinceInfo.style.visibility = 'hidden';
      }


    }
  }
</script>
<script>
  let line = new lineMap();
  line.init();
</script>

</html>